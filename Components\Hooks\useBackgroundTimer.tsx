import { useRef, useState, useCallback } from 'react';

// 🔄 CISCO: Types pour le timer ULTRA-SIMPLIFIÉ
type TimerStatus = 'stopped' | 'running' | 'paused';
type TimerType = 'stopwatch' | 'countdown';

interface UseBackgroundTimerProps {
  onStop?: (elapsedTime: number) => void;
  onCountdownFinish?: () => void;
  enabled?: boolean;
}

// 🔄 CISCO: Hook timer ULTRA-SIMPLIFIÉ - ZÉRO complexité, ZÉRO bug
export const useBackgroundTimer = ({
  onStop,
  onCountdownFinish,
  enabled = true
}: UseBackgroundTimerProps = {}) => {
  const intervalRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);

  // État simple
  const [elapsedTime, setElapsedTime] = useState(0);
  const [remainingTime, setRemainingTime] = useState(0);
  const [status, setStatus] = useState<TimerStatus>('stopped');
  const [type, setType] = useState<TimerType>('stopwatch');

  // 🔄 Système de fallback sans Web Worker
  const handleFallbackTimer = useCallback((type: string, data?: any) => {
    switch (type) {
      case 'start':
        if (data?.type === 'countdown') {
          startFallbackCountdown(data.duration);
        } else {
          startFallbackStopwatch();
        }
        break;
      case 'pause':
        pauseFallbackTimer();
        break;
      case 'resume':
        resumeFallbackTimer();
        break;
      case 'stop':
        stopFallbackTimer();
        break;
    }
  }, []);

  // 🧹 Nettoyage
  const cleanup = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // ⏱️ CISCO: Démarrer chronomètre - ULTRA SIMPLE
  const startStopwatch = useCallback(() => {
    cleanup();

    setElapsedTime(0);
    setRemainingTime(0);
    setStatus('running');
    setType('stopwatch');

    startTimeRef.current = Date.now() - pausedTimeRef.current;

    intervalRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      setElapsedTime(elapsed);
    }, 100);

    // 🔊 Son
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('start');
    }
  }, [cleanup]);

  // ⏰ CISCO: Démarrer compte à rebours - ULTRA SIMPLE
  const startCountdown = useCallback((durationMs: number) => {
    cleanup();

    setElapsedTime(0);
    setRemainingTime(durationMs);
    setStatus('running');
    setType('countdown');

    startTimeRef.current = Date.now();

    intervalRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      const remaining = Math.max(0, durationMs - elapsed);
      setRemainingTime(remaining);

      if (remaining <= 0) {
        cleanup();
        setStatus('stopped');
        setRemainingTime(0);
        pausedTimeRef.current = 0;

        // 🔊 Son de fin
        if (typeof (window as any).playTimerSound === 'function') {
          (window as any).playTimerSound('countdown_finish');
        }
        if (onCountdownFinish) {
          onCountdownFinish();
        }
      }
    }, 100);

    // 🔊 Son
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('start');
    }
  }, [cleanup, onCountdownFinish]);

  // ⏸️ CISCO: Pause - ULTRA SIMPLE
  const pause = useCallback(() => {
    cleanup();
    setStatus('paused');
    pausedTimeRef.current = Date.now() - startTimeRef.current;

    // 🔊 Son
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('pause');
    }
  }, [cleanup]);

  // ▶️ CISCO: Reprendre - ULTRA SIMPLE
  const resume = useCallback(() => {
    if (status === 'paused') {
      if (type === 'countdown') {
        startCountdown(remainingTime);
      } else {
        startStopwatch();
      }
    }

    // 🔊 Son
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('resume');
    }
  }, [status, type, remainingTime, startCountdown, startStopwatch]);

  // ⏹️ CISCO: Arrêter - ULTRA SIMPLE
  const stop = useCallback(() => {
    cleanup();
    setElapsedTime(0);
    setRemainingTime(0);
    setStatus('stopped');
    setType('stopwatch');
    pausedTimeRef.current = 0;

    // 🔊 Son
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('stop');
    }
  }, [cleanup]);

  // 📊 CISCO: Obtenir le statut - ULTRA SIMPLE
  const getStatus = useCallback(() => {
    return { elapsedTime, remainingTime, status, type };
  }, [elapsedTime, remainingTime, status, type]);

  // 🔄 CISCO: Pause forcée - ULTRA SIMPLE
  const forcePause = useCallback(() => {
    if (status === 'running') {
      pause();
    }
  }, [status, pause]);

  return {
    // État
    elapsedTime,
    remainingTime,
    status,
    type,

    // Actions
    startStopwatch,
    startCountdown,
    pause,
    resume,
    stop,
    forcePause,
    getStatus,

    // Utilitaires
    isWorkerSupported: false // Plus de Web Worker
  };
};
